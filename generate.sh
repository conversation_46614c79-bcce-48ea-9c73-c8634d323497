#!/bin/bash

#if [ -z "$1" ]
#then
#  	echo "Project id not set, using the default 111"
#  	proj=111
#else
#	proj=$1
#fi
#
#printf "\u2192 Finding the folder of the generator \n"
##cd /var/www/carenet/generator/public
#cd ./generator/public
#printf "\u2192 Starting at: %s \n" "$(date)"
#printf "\u2192 Running generator for project %d, output: \n\n" "$proj"
#php cmdgenerate.php $proj
#printf "\n\n\u2192 Finished at: %s \n" "$(date)"

if [ -z "$1" ]
then
  	echo "Project id not set, using the default 111"
  	proj=111
else
	proj=$1
fi

printf "\u2192 Finding the folder of the generator \n"
cd ../generator/public
printf "\u2192 Starting at: %s \n" "$(date)"
printf "\u2192 Running generator for project %d, output: \n\n" "$proj"
php cmdgenerate.php $proj
printf "\n\n\u2192 Finished at: %s \n" "$(date)"