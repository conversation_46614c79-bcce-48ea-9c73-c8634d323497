{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "57211dc5c619d183df6361f5d7d57fe5", "packages": [], "packages-dev": [{"name": "phpstan/phpstan", "version": "2.1.1", "source": {"type": "git", "url": "https://github.com/phpstan/phpstan.git", "reference": "cd6e973e04b4c2b94c86e8612b5a65f0da0e08e7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpstan/zipball/cd6e973e04b4c2b94c86e8612b5a65f0da0e08e7", "reference": "cd6e973e04b4c2b94c86e8612b5a65f0da0e08e7", "shasum": ""}, "require": {"php": "^7.4|^8.0"}, "conflict": {"phpstan/phpstan-shim": "*"}, "bin": ["phpstan", "phpstan.phar"], "type": "library", "autoload": {"files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPStan - PHP Static Analysis Tool", "keywords": ["dev", "static analysis"], "support": {"docs": "https://phpstan.org/user-guide/getting-started", "forum": "https://github.com/phpstan/phpstan/discussions", "issues": "https://github.com/phpstan/phpstan/issues", "security": "https://github.com/phpstan/phpstan/security/policy", "source": "https://github.com/phpstan/phpstan-src"}, "funding": [{"url": "https://github.com/ondrejmirtes", "type": "github"}, {"url": "https://github.com/phpstan", "type": "github"}], "time": "2025-01-05T16:43:48+00:00"}, {"name": "rector/rector", "version": "2.0.6", "source": {"type": "git", "url": "https://github.com/rectorphp/rector.git", "reference": "fa0cb009dc3df084bf549032ae4080a0481a2036"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/rectorphp/rector/zipball/fa0cb009dc3df084bf549032ae4080a0481a2036", "reference": "fa0cb009dc3df084bf549032ae4080a0481a2036", "shasum": ""}, "require": {"php": "^7.4|^8.0", "phpstan/phpstan": "^2.1.1"}, "conflict": {"rector/rector-doctrine": "*", "rector/rector-downgrade-php": "*", "rector/rector-phpunit": "*", "rector/rector-symfony": "*"}, "suggest": {"ext-dom": "To manipulate phpunit.xml via the custom-rule command"}, "bin": ["bin/rector"], "type": "library", "autoload": {"files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Instant Upgrade and Automated Refactoring of any PHP code", "keywords": ["automation", "dev", "migration", "refactoring"], "support": {"issues": "https://github.com/rectorphp/rector/issues", "source": "https://github.com/rectorphp/rector/tree/2.0.6"}, "funding": [{"url": "https://github.com/tomasvotruba", "type": "github"}], "time": "2025-01-06T10:38:36+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": {}, "prefer-stable": false, "prefer-lowest": false, "platform": {}, "platform-dev": {}, "plugin-api-version": "2.6.0"}