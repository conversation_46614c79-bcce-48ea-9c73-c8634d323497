#!/bin/bash

nocolor='\033[0m'
red='\033[0;31m'
orange='\033[0;33m'
green='\033[0;32m'

id=$(/Applications/MAMP/Library/bin/mysql --user=root --password=root actinum_studiov3_project_119_tenant1 -s --skip-column-names -e "SELECT \`tenant_id\` FROM \`domain\` WHERE \`domain\` = 'localhost:8888';" | awk '{print $1}')

printf "${green}Currently you are using tenant ${id}...\n\n${nocolor}"

if [[ "${id}" == "1" ]]
then
  printf "${red}The current tenant is already 1, canceling the switchback...\n\n${nocolor}"
  exit 1
fi

printf "${green}Switching your local tenant back to tenant 1...\n\n${nocolor}"

/Applications/MAMP/Library/bin/mysql --user=root --password=root actinum_studiov3_project_119_tenant1 -s -e "REPLACE INTO \`domain\` (\`id\`, \`tenant_id\`, \`createdby_id\`, \`updatedby_id\`, \`_ordering\`, \`domain\`, \`active\`, \`createdon\`, \`updatedon\`, \`readonly\`, \`demodummy\`, \`state\`, \`zoeknaam\`, \`version\`) VALUES (1, 1, NULL, NULL, NULL, 'localhost:8888', 1, NULL, NULL, 0, 0, NULL, NULL, 1);"

printf "${green}Deleting local tenant ${id} from the tenants table,...\n\n${nocolor}"

/Applications/MAMP/Library/bin/mysql --user=root --password=root actinum_studiov3_project_119_tenant1 -s -e "DELETE FROM \`tenant\` WHERE \`id\` = ${id};"

printf "${green}Dropping the database actinum_studiov3_project_111_tenant${id}...\n\n${nocolor}"

/Applications/MAMP/Library/bin/mysql --user=root --password=root -s -e "DROP DATABASE IF EXISTS actinum_studiov3_project_111_tenant${id};"

printf "${green}Switched back to tenant 1 and deleted the tenant ${id}...\n\n${nocolor}"