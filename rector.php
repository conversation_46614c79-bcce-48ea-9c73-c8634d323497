<?php

declare(strict_types=1);

use <PERSON>\Config\RectorConfig;
use <PERSON>\Php54\Rector\Array_\LongArrayToShortArrayRector;
use <PERSON>\Php82\Rector\Encapsed\VariableInStringInterpolationFixerRector;
use <PERSON>\Php84\Rector\Param\ExplicitNullableParamTypeRector;

return RectorConfig::configure()
    ->withRules([
        ExplicitNullableParamTypeRector::class,
        LongArrayToShortArrayRector::class,
        VariableInStringInterpolationFixerRector::class
    ])
    ->withPaths([
        __DIR__ . '/private',
        __DIR__ . '/public',
    ])
    ->withSkip([
        // directories
        __DIR__ . '/private/cache/*',
        // __DIR__ . '/private/generated/*',
        __DIR__ . '/private/custom/configs/*',
        __DIR__ . '/public/cache/*',
    ])
    ->withImportNames(
        importNames: false,
        importDocBlockNames: false,
        importShortClasses: false,
    )
    ->with<PERSON><PERSON><PERSON>l(600, 8, 10);
