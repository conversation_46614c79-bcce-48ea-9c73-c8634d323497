#!/bin/bash

nocolor='\033[0m'
red='\033[0;31m'
orange='\033[0;33m'
green='\033[0;32m'

if [ -z "$1" ]
then
  printf "${red}Please prove a tenant id...\n\n${nocolor}"
  exit 0
else
	id=$1
fi

if [ -z "$2" ]
then
  printf "${orange}No tenant name provided, using the id as the name...\n\n${nocolor}"
  name="tenant ${id}"
else
	name=$2
fi

if [ -z "$3" ]
then
  printf "${orange}No 'full' option provided, using 'full' wit the value 'false'...\n\n${nocolor}"
  full='false'
else
	full=$3
fi

if [[ "${full}" == "true" ]]
then
  printf "${green}Making a full backup of database actinum_studiov3_project_111_tenant${id}...\n\n${nocolor}"
  ssh <EMAIL> "mysqldump --user=carenet_main --password=T8xBj5VwIXl08iP9 actinum_studiov3_project_111_tenant${id} | gzip > ~/carenetfoodsolutions.nl/public_html/${id}_clone_4asfd234js_clean.sql.gz | exit"
else
  printf "${green}Making a partial backup of database actinum_studiov3_project_111_tenant${id}...\n\n${nocolor}"
  ssh <EMAIL> "mysqldump --user=carenet_main --password=T8xBj5VwIXl08iP9 actinum_studiov3_project_111_tenant${id} --ignore-table=actinum_studiov3_project_111_tenant${id}.entitychangelog | gzip > ~/carenetfoodsolutions.nl/public_html/${id}_clone_4asfd234js_clean.sql.gz | exit"
fi

printf "${green}Backup is been made, starting to download the backup...\n\n${nocolor}"

curl -O "https://download.normeconline.nl/${id}_clone_4asfd234js_clean.sql.gz"

printf "${green}Backup has been downloaded, unpacking the backup...\n\n${nocolor}"

gunzip "${id}_clone_4asfd234js_clean.sql.gz"

printf "${green}Backup has been unpacked, dropping your local database and importing unpacked backup...\n\n${nocolor}"

/Applications/MAMP/Library/bin/mysql --user=root --password=root -s -e "SET GLOBAL max_allowed_packet=104857600000"
/Applications/MAMP/Library/bin/mysql --user=root --password=root -s -e "DROP DATABASE IF EXISTS actinum_studiov3_project_111_tenant${id}; CREATE DATABASE actinum_studiov3_project_111_tenant${id}"
/Applications/MAMP/Library/bin/mysql --user=root --password=root -s actinum_studiov3_project_111_tenant${id} < "${id}_clone_4asfd234js_clean.sql"

printf "${green}Switching your local tenant to tenant ${id}...\n\n${nocolor}"

/Applications/MAMP/Library/bin/mysql --user=root --password=root actinum_studiov3_project_119_tenant1 -s -e "REPLACE INTO \`tenant\` (\`id\`, \`createdby_id\`, \`updatedby_id\`, \`company\`, \`active\`, \`customerid\`, \`createdon\`, \`updatedon\`, \`readonly\`, \`demodummy\`, \`state\`, \`zoeknaam\`, \`version\`) VALUES (${id}, 1, 2, 'Backup from ${name}', 1, '1', NOW(), NOW(), 0, 0, NULL, 'Backup from ${name}', 1);"
/Applications/MAMP/Library/bin/mysql --user=root --password=root actinum_studiov3_project_119_tenant1 -s -e "REPLACE INTO \`domain\` (\`id\`, \`tenant_id\`, \`createdby_id\`, \`updatedby_id\`, \`_ordering\`, \`domain\`, \`active\`, \`createdon\`, \`updatedon\`, \`readonly\`, \`demodummy\`, \`state\`, \`zoeknaam\`, \`version\`) VALUES (1, ${id}, NULL, NULL, NULL, 'localhost:8888', 1, NULL, NULL, 0, 0, NULL, NULL, 1);"

printf "${green}Cleaning up the backup on your local machine...\n\n${nocolor}"

rm "${id}_clone_4asfd234js_clean.sql"

printf "${green}Cleaning up the backup on the remote machine...\n\n${nocolor}"

ssh <EMAIL> "rm ~/carenetfoodsolutions.nl/public_html/${id}_clone_4asfd234js_clean.sql.gz | exit"