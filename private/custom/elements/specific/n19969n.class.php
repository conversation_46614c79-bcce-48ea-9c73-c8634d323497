<?php
namespace nl\actinum\custom\elements\specific {



/*[GENERATEDBYBUILDER_CLASSNAME*/
class n19969n extends \nl\actinum\custom\elements\generic\Linkform
/*GENERATEDBYBUILDER_CLASSNAME]*/
    //implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode
                                                                                                                            {

/*[GENERATEDBYBUILDER*/

    public static $tablename = 'actiontype';
    public static $selectdql = 'settingsactiontype.zoeknaam';
    public static $subtitle = '';
    public static $showsubtitle = false;
    public static $dbfield = 'settingsactiontype';
    public static $defaultvalue = '';
    public static $nullallowed = NULL;
    public static $dbtrigger = NULL;
    public static $index = NULL;
    public static $placeholder = NULL;
    public static $required = NULL;
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n19969n';
    public static $parentElementid = 'n19968n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = [
];
/*GENERATEDBYBUILDER]*/

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.CustomJs.n19969n'] =
<<<'JS'
Actinum.Application.CustomElements.n19969n = new Class({ Extends: Actinum.Application.Elements.Linkform,

    getMode: function() { return 'add'; },

    initialize: function initialize(elementid, properties, data, settings, structure) {
        var ss = $N('n19452n').getSetting('actiessettings');
        if (ss && ss['actiontypes']){
            data['raw'] = ss['actiontypes'];
        }
        return this.parent(elementid, properties, data, settings, structure);
    }, 
    
    renderadd: function renderadd(){
        var el = this.parent();
        
        var pos = 0;
        
        this.getData().list.each(function(item){
            if(item.id == 1){
                pos++;
            }
            
            if(item.id == 2){
                pos++;
            }
            if(item.count > 0 || item.count == undefined){
            
                if(item.id == 3 ){
                    pos++;
                    var plus_sign_1 = new Element('span', {
                        'text': '+',
                        'class': 'plus_sign_1 pos_' + pos
                    });
                    
                    el.adopt(plus_sign_1);
                }
                
                if(item.id == 4 ){
                    pos++;
                    var plus_sign_2 = new Element('span', {
                        'text': '+',
                        'class': 'plus_sign_2 pos_' + pos
                    });
                    
                    el.adopt(plus_sign_2);
                }
                
                
            }else{
                el.children[0].children[pos].remove();
            }
        })
        
        return el;
    }

});
JS;
        return $result;
    }



    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.CustomCss.n19969n'] =
<<<'CSS'

    .n19969n {
        position: relative;
        margin-bottom: 0px;
        padding-bottom: 0px;
    }
    
    .n19969n .plus_sign_1 {
        position: absolute;
        top: 47px;
        left: 54px;
    }
    
    .n19969n .plus_sign_1.pos_1{
        top: 11px;
    }
    .n19969n .plus_sign_1.pos_2{
        top: 29px;
    }
    
    .n19969n .plus_sign_2 {
        position: absolute;
        top: 65px;
        left: 63px;
    }
    
    .n19969n .plus_sign_2.pos_1 {
        top: 11px;
    }
    
    .n19969n .plus_sign_2.pos_2 {
        top: 29px;
    }
    
    .n19969n .plus_sign_2.pos_3 {
        top: 47px;
    }

CSS;
        return $result;
    }

    public static function getList($drw, $settings) {
        $list = parent::getList($drw, $settings);

        $user = \nl\actinum\framework\application\Registry::get('login')->getUser();

        foreach($list as $listkey => $listitem){

            $hasprofile = true;
            switch($listitem['id']){

                case 1: // inspecties
                    $hasprofile = $user->hasProfile(
                        static::getEM()->find('nl\actinum\custom\tables\profile', 20)
                    );
                    break;
                case 2: // registraties
                    $hasprofile = $user->hasProfile(
                        static::getEM()->find('nl\actinum\custom\tables\profile', 91)
                    );
                    break;
                case 3: // memo
                    $hasprofile = $user->hasProfile(
                        static::getEM()->find('nl\actinum\custom\tables\profile', 154)
                    );
                    $query = static::getEM()->createQuery('
                        SELECT COUNT(memocategory.id) FROM \nl\actinum\custom\tables\memocategory memocategory 
                        ');

                    $memoCategories = $query->getArrayResult();
                    $list[$listkey]['count'] = $memoCategories[0][1];
                    break;
                case 4: // acties
                    $hasprofile = $user->hasProfile(
                        static::getEM()->find('nl\actinum\custom\tables\profile', 154)
                    );
                    $query = static::getEM()->createQuery('
                        SELECT COUNT(actioncategory.id) FROM \nl\actinum\custom\tables\actioncategory actioncategory 
                        ');

                    $actieCategories = $query->getArrayResult();
                    $list[$listkey]['count'] = $actieCategories[0][1];
                    break;
//                case 4: // overige, deze is eigenlijk overbodig omdat je deze linkform niet ziet als je dit recht niet hebt
//                    $hasprofile = $user->hasProfile(
//                        static::getEM()->find('nl\actinum\custom\tables\profile', 190)
//                    );
//                    break;
            }

            if(!$hasprofile){
                unset($list[$listkey]);
            }
        }

        ray($user->hasProfile(
            static::getEM()->find('nl\actinum\custom\tables\profile', 91)
        ));

        $list = array_values($list);
        return $list;
    }

    public static function getSortings(){
        $result = [];

        $temp = [];
        $temp['sort'] = static::$dbfield.'.id';
        $temp['order'] = 'ASC';
        $result[] = $temp;

        return $result;
    }
        


}

}
?>