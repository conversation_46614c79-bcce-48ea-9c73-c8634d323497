<?php
namespace nl\actinum\generated\elements\specific {

//Element of type: Subpagelink
class n11263n extends \nl\actinum\custom\elements\generic\Subpagelink {

    
    public static $subpage = 'n11238n';
    public static $title = 'Afnemers';
    public static $subtitle = NULL;
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n11263n';
    public static $parentElementid = 'n11261n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    
    public static $childrenElementIds =  [
];

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.GeneratedJs.n11263n'] =
<<<'EOD'
    
EOD;
        return $result;
    }


    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.GeneratedCss.n11263n'] =
<<<'EOD'

EOD;
        return $result;
    }


}
}
?>