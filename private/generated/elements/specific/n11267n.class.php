<?php
namespace nl\actinum\generated\elements\specific {

//Element of type: Gridroweditlink
class n11267n extends \nl\actinum\custom\elements\generic\Gridroweditlink {

    
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n11267n';
    public static $parentElementid = 'n9563n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    
    public static $childrenElementIds =  [
];

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.GeneratedJs.n11267n'] =
<<<'EOD'
    
EOD;
        return $result;
    }


    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.GeneratedCss.n11267n'] =
<<<'EOD'

EOD;
        return $result;
    }


}
}
?>