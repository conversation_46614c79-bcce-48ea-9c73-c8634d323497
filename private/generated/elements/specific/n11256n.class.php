<?php
namespace nl\actinum\generated\elements\specific {

//Element of type: Subpagelinkcontainer
class n11256n extends \nl\actinum\custom\elements\generic\Subpagelinkcontainer {

    
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n11256n';
    public static $parentElementid = 'n11253n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    
    public static $childrenElementIds =  [
  0 => 'n11259n',
  1 => 'n11260n',
];

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.GeneratedJs.n11256n'] =
<<<'EOD'
    
EOD;
        return $result;
    }


    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.GeneratedCss.n11256n'] =
<<<'EOD'

EOD;
        return $result;
    }


}
}
?>