<?php
namespace nl\actinum\generated\elements\specific {

//Element of type: Form
class n11264n extends \nl\actinum\custom\elements\generic\Form {

    
    public static $style = '._this_ {
  background-color: white;
}
';
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n11264n';
    public static $parentElementid = 'n11160n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    
    public static $childrenElementIds =  [
  0 => 'n11265n',
];

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.GeneratedJs.n11264n'] =
<<<'EOD'
    
EOD;
        return $result;
    }


    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.GeneratedCss.n11264n'] =
<<<'EOD'
.n11264n{
  background-color: white;
}

EOD;
        return $result;
    }


}
}
?>