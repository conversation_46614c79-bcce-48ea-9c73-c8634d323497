<?php
namespace nl\actinum\generated\elements\specific {

//Element of type: Forminputstring
class n11277n extends \nl\actinum\custom\elements\generic\Forminputstring {

    
    public static $subtitle = 'Nummer';
    public static $showsubtitle = true;
    public static $dbfield = 'number';
    public static $defaultvalue = NULL;
    public static $nullallowed = NULL;
    public static $dbtrigger = NULL;
    public static $index = NULL;
    public static $placeholder = NULL;
    public static $required = NULL;
    public static $style = '._this_ {
  width: 90px;
}
';
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n11277n';
    public static $parentElementid = 'n11148n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    
    public static $childrenElementIds =  [
];

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.GeneratedJs.n11277n'] =
<<<'EOD'
    
EOD;
        return $result;
    }


    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.GeneratedCss.n11277n'] =
<<<'EOD'
.n11277n{
  width: 90px;
}

EOD;
        return $result;
    }


}
}
?>