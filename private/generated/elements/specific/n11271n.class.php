<?php
namespace nl\actinum\generated\elements\specific {

//Element of type: Gridpreset
class n11271n extends \nl\actinum\custom\elements\generic\Gridpreset {

    
    public static $title = 'Leveranciers';
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n11271n';
    public static $parentElementid = 'n9252n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    
    public static $childrenElementIds =  [
  0 => 'n11272n',
];

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.GeneratedJs.n11271n'] =
<<<'EOD'
    
EOD;
        return $result;
    }


    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.GeneratedCss.n11271n'] =
<<<'EOD'

EOD;
        return $result;
    }


}
}
?>