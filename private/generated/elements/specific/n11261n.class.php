<?php
namespace nl\actinum\generated\elements\specific {

//Element of type: Subpagelinkcontainer
class n11261n extends \nl\actinum\custom\elements\generic\Subpagelinkcontainer {

    
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n11261n';
    public static $parentElementid = 'n11160n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    
    public static $childrenElementIds =  [
  0 => 'n11262n',
  1 => 'n11263n',
];

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.GeneratedJs.n11261n'] =
<<<'EOD'
    
EOD;
        return $result;
    }


    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.GeneratedCss.n11261n'] =
<<<'EOD'

EOD;
        return $result;
    }


}
}
?>