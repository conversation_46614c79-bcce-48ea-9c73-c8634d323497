<?php
namespace nl\actinum\generated\elements\specific {

//Element of type: Doctrinefilter
class n11272n extends \nl\actinum\custom\elements\generic\Doctrinefilter {

    
    public static $filterstring = '1=1';
    public static $filterdescription = NULL;
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n11272n';
    public static $parentElementid = 'n11271n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    
    public static $childrenElementIds =  [
];

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.GeneratedJs.n11272n'] =
<<<'EOD'
    
EOD;
        return $result;
    }


    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.GeneratedCss.n11272n'] =
<<<'EOD'

EOD;
        return $result;
    }


}
}
?>