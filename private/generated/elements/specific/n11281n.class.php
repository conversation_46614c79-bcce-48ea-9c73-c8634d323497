<?php
namespace nl\actinum\generated\elements\specific {

//Element of type: Forminputcontainer
class n11281n extends \nl\actinum\custom\elements\generic\Forminputcontainer {

    
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n11281n';
    public static $parentElementid = 'n9304n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    
    public static $childrenElementIds =  [
  0 => 'n11282n',
  1 => 'n11283n',
];

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.GeneratedJs.n11281n'] =
<<<'EOD'
    
EOD;
        return $result;
    }


    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.GeneratedCss.n11281n'] =
<<<'EOD'

EOD;
        return $result;
    }


}
}
?>