#!/bin/bash

echo "List all the files which has only changed in the line endings"
git diff --name-only --diff-filter=M | while read file; do
  if git diff --ignore-space-at-eol "$file" | grep -q '^'; then
    :
  else
    echo "$file"
  fi
done > line-ending-files.txt

echo "Restore every file which are on the list of only changed line endings"
cat line-ending-files.txt | xargs git restore

echo "Remove the file line-ending-files.txt"
rm line-ending-files.txt

echo "List all the files which has no changes in a git dif"
git status -s | grep '^ M' | cut -c4- | while read file; do
  if git diff "$file" | grep -q '^'; then
    :
  else
    echo "$file"
  fi
done > identical-files.txt

echo "Restore every file which are on the list of not changed in a git diff"
cat identical-files.txt | xargs git restore

echo "Remove the file identical-files.txt"
rm identical-files.txt